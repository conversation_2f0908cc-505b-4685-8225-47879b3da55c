'use client';

import { useLocale, useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import TipTapViewer from '@/components/TipTapViewer';
import { useRouter } from '@/libs/i18nNavigation';
import { type GeneratePromptResponse, type Prompt, promptsService } from '@/services/prompts';

export default function PromptDetailPage() {
  const t = useTranslations('Prompts');
  const locale = useLocale();
  const params = useParams();
  const router = useRouter();
  const promptId = Number.parseInt(params?.id as string);

  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [result, setResult] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState('google/gemini-2.0-flash-exp:free');
  const [editablePromptText, setEditablePromptText] = useState<string>('');

  const availableModels = [
    { id: 'google/gemini-2.0-flash-exp:free', name: 'Google/Gemini-2.0-Flash-Exp:free' },
    { id: 'google/gemini-pro:free', name: 'Google/Gemini-Pro:free' },
    { id: 'meta-llama/llama-3.2-3b-instruct:free', name: 'Meta-Llama/Llama-3.2-3B-Instruct:free' },
  ];

  useEffect(() => {
    const fetchPrompt = async () => {
      try {
        setLoading(true);
        const data = await promptsService.getPromptById(promptId, locale);
        setPrompt(data);
        if (data?.prompt_text) {
          setEditablePromptText(data.prompt_text);
        }
      } catch (error) {
        console.error('Error fetching prompt:', error);
        toast.error('Failed to load prompt');
        router.push('/prompts');
      } finally {
        setLoading(false);
      }
    };

    fetchPrompt();
  }, [promptId, locale, router]);

  const handleCopyPrompt = async () => {
    if (!prompt?.prompt_text) {
      return;
    }

    try {
      await navigator.clipboard.writeText(prompt.prompt_text);
      toast.success(t('copy_success'));
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleGenerateResult = async () => {
    if (!editablePromptText.trim()) {
      toast.error('Please enter prompt text');
      return;
    }

    try {
      setGenerating(true);
      const response: GeneratePromptResponse = await promptsService.generatePromptResult({
        prompt_text: editablePromptText,
        model: selectedModel,
        prompt_id: prompt?.id,
      }) || ({} as GeneratePromptResponse);
      if (response) {
        setResult(response.result);
        toast.success(t('generate_success'));
      }
    } catch (error) {
      console.error('Error generating result:', error);
      toast.error(t('generate_error'));
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="size-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="py-12 text-center">
        <div className="text-lg text-gray-400">Prompt not found</div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <button
          type="button"
          onClick={() => router.back()}
          className="mb-4 inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          <svg className="mr-2 size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
          </svg>
          Menu
        </button>

        <div className="flex items-start justify-start">
          <div className="flex-1">
            <div className="mb-2 flex items-center gap-2">
              <span className="flex size-6 items-center justify-center rounded-full bg-yellow-400 text-sm">💡</span>
              <h1 className="text-2xl font-bold text-gray-900">{prompt.title}</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Left Column - Prompt */}
        <div className="space-y-6">
          <div className="rounded-lg border bg-white p-6 shadow-sm">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">{t('prompt_detail')}</h2>
              <button
                type="button"
                onClick={handleCopyPrompt}
                className="flex items-center gap-2 rounded-md bg-blue-100 px-3 py-1 text-sm text-blue-700 transition-colors hover:bg-blue-200"
              >
                <svg className="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                {t('copy_prompt')}
              </button>
            </div>
          </div>

          {prompt.optimization_guide && (
            <div className="mb-6">
              <h3 className="mb-2 text-sm font-medium text-gray-700">Optimization Guide:</h3>
              <TipTapViewer
                content={prompt.optimization_guide}
                editable
                onContentChange={setEditablePromptText}
                className="leading-relaxed text-gray-700"
              />
            </div>
          )}

          {/* Model Selection */}
          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
              {t('model_selection')}
            </label>
            <select
              value={selectedModel}
              onChange={e => setSelectedModel(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              {availableModels.map(model => (
                <option key={model.id} value={model.id}>
                  {model.name}
                </option>
              ))}
            </select>
          </div>

          <button
            type="button"
            onClick={handleGenerateResult}
            disabled={generating}
            className="flex w-full items-center justify-center gap-2 rounded-lg bg-purple-600 px-4 py-3 font-medium text-white transition-colors duration-200 hover:bg-purple-700 disabled:bg-purple-400"
          >
            {generating
              ? (
                  <>
                    <div className="size-4 animate-spin rounded-full border-b-2 border-white"></div>
                    {t('generating')}
                  </>
                )
              : (
                  <>
                    <svg className="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    {t('generate_result')}
                    {' '}
                    →
                  </>
                )}
          </button>
        </div>
      </div>

      {/* Right Column - Result */}
      <div className="space-y-6">
        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">{t('result_detail')}</h2>
            <button
              type="button"
              onClick={handleCopyPrompt}
              className="flex items-center gap-2 rounded-md bg-blue-100 px-3 py-1 text-sm text-blue-700 transition-colors hover:bg-blue-200"
            >
              <svg className="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              {t('copy_prompt')}
            </button>
          </div>

          <div className="min-h-[300px] rounded-lg bg-gray-50 p-4">
            {generating
              ? (
                  <div className="flex h-full items-center justify-center">
                    <div className="text-center">
                      <div className="mx-auto mb-4 size-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
                      <p className="text-gray-600">Generating result...</p>
                    </div>
                  </div>
                )
              : result
                ? (
                    <TipTapViewer
                      content={result}
                      className="leading-relaxed text-gray-700"
                    />
                  )
                : (
                    <div className="flex h-full items-center justify-center text-gray-400">
                      Chưa có kết quả. Hãy chạy prompt!
                    </div>
                  )}
          </div>
        </div>
      </div>
    </div>
  );
}
