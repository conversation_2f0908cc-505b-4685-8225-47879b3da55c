import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CampaignsService } from './campaigns.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignFilterDto } from './dto/campaign-filter.dto';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';
import * as path from 'path';

interface RequestWithUser extends Request {
  user: {
    sub: number;
    email: string;
    role: string;
    status: string;
  };
}

@Controller('campaigns')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class CampaignsController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Post()
  async create(
    @Body() createCampaignDto: CreateCampaignDto,
    @Req() req: RequestWithUser,
  ) {
    try {
      // JWT strategy returns user.sub as the user ID
      const userId = req.user?.sub;
      if (!userId) {
        throw new HttpException(
          'User not authenticated',
          HttpStatus.UNAUTHORIZED,
        );
      }
      return await this.campaignsService.create(createCampaignDto, userId);
    } catch (error) {
      throw new HttpException(
        'Failed to create campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async findAll(@Query() filterDto: CampaignFilterDto) {
    try {
      return await this.campaignsService.findAll(filterDto);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaigns: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.campaignsService.findOne(id);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/stats')
  async getCampaignStats(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.campaignsService.getCampaignStats(id);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaign stats: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCampaignDto: UpdateCampaignDto,
  ) {
    try {
      return await this.campaignsService.update(id, updateCampaignDto);
    } catch (error) {
      throw new HttpException(
        'Failed to update campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/send')
  async sendCampaign(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.campaignsService.sendCampaign(id);
      return { message: 'Campaign sending started successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to send campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.campaignsService.remove(id);
      return { message: 'Campaign deleted successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to delete campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('upload-image')
  @UseInterceptors(
    FileInterceptor('image', {
      storage: diskStorage({
        destination: (req, file, cb) => {
          const uploadPath = path.join(
            process.cwd(),
            'uploads',
            'campaign-images',
          );
          if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix =
            Date.now() + '-' + Math.round(Math.random() * 1e9);
          cb(null, `campaign-${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp)$/)) {
          return cb(new Error('Only image files are allowed!'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
      },
    }),
  )
  async uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: RequestWithUser,
  ) {
    try {
      // Verify user authentication
      const userId = req.user?.sub;
      if (!userId) {
        throw new HttpException(
          'User not authenticated',
          HttpStatus.UNAUTHORIZED,
        );
      }

      if (!file) {
        throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
      }

      const baseUrl = process.env.APP_URL || 'http://localhost:3000';
      const imageUrl = `${baseUrl}/uploads/campaign-images/${file.filename}`;

      console.log('Image uploaded successfully:', {
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        imageUrl: imageUrl,
        filePath: path.join(
          process.cwd(),
          'uploads',
          'campaign-images',
          file.filename,
        ),
      });

      return {
        success: true,
        imageUrl,
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
      };
    } catch (error) {
      throw new HttpException(
        'Failed to upload image: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('test-image/:filename')
  async testImage(@Param('filename') filename: string, @Res() res: Response) {
    try {
      const imagePath = path.join(
        process.cwd(),
        'uploads',
        'campaign-images',
        filename,
      );
      console.log('Testing image access:', imagePath);

      if (fs.existsSync(imagePath)) {
        return res.sendFile(imagePath);
      } else {
        return res
          .status(404)
          .json({ error: 'Image not found', path: imagePath });
      }
    } catch (error) {
      return res
        .status(500)
        .json({ error: 'Failed to serve image', message: error.message });
    }
  }
}

// Public controller for tracking
@Controller('campaigns/track')
export class CampaignTrackingController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Get(':trackingId')
  async trackEmailOpen(
    @Param('trackingId') trackingId: string,
    @Res() res: Response,
  ) {
    try {
      await this.campaignsService.trackEmailOpen(trackingId);

      // Return a 1x1 transparent pixel
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64',
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      });

      res.send(pixel);
    } catch (error) {
      // Return empty pixel even on error to avoid breaking email display
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64',
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
      });

      res.send(pixel);
    }
  }
}
